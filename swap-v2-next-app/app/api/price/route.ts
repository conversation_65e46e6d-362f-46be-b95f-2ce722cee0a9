import { type NextRequest } from "next/server";

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams;

  try {
    const url = `https://api.0x.org/swap/permit2/price?${searchParams}`;
    console.log("Fetching price from:", url);

    const res = await fetch(url, {
      headers: {
        "0x-api-key": process.env.NEXT_PUBLIC_ZEROEX_API_KEY as string,
        "0x-version": "v2",
      },
    });

    console.log("API Response status:", res.status, res.statusText);

    if (!res.ok) {
      const errorText = await res.text();
      console.error("API Error:", res.status, errorText);
      return Response.json({ error: `API Error: ${res.status} ${errorText}` }, { status: res.status });
    }

    const data = await res.json();
    console.log("price data", data);

    return Response.json(data);
  } catch (error) {
    console.error("Price API Error:", error);
    return Response.json({ error: "Internal server error" }, { status: 500 });
  }
}
